const mongoose = require("mongoose");

const newsletterSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    content: { type: String, required: true }, // HTML content
    excerpt: { type: String }, // Short description for preview
    coverImage: { type: String }, // Newsletter cover image
    status: {
      type: String,
      enum: ["draft", "scheduled", "sent"],
      default: "draft",
    },
    scheduledAt: { type: Date }, // When to send the newsletter
    sentAt: { type: Date }, // When the newsletter was actually sent
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    recipients: {
      totalCount: { type: Number, default: 0 },
      sentCount: { type: Number, default: 0 },
      failedCount: { type: Number, default: 0 },
    },
    tags: [{ type: String }], // For categorization
    isDeleted: { type: Boolean, default: false },
    activeStatus: { type: Number, default: 1 }, // 1: active, 0: inactive
  },
  { timestamps: true }
);

const subscriptionSchema = new mongoose.Schema(
  {
    email: { type: String, required: true, unique: true },
    firstName: { type: String },
    lastName: { type: String },
    isActive: { type: Boolean, default: true },
    subscribedAt: { type: Date, default: Date.now },
    unsubscribedAt: { type: Date },
    preferences: {
      frequency: {
        type: String,
        enum: ["daily", "weekly", "monthly"],
        default: "weekly",
      },
      categories: [{ type: String }], // Which types of newsletters they want
    },
    verificationToken: { type: String }, // For email verification
    isVerified: { type: Boolean, default: false },
    unsubscribeToken: { type: String }, // For one-click unsubscribe
  },
  { timestamps: true }
);

// Index for better performance
newsletterSchema.index({ status: 1, scheduledAt: 1 });
newsletterSchema.index({ createdBy: 1, createdAt: -1 });
subscriptionSchema.index({ email: 1, isActive: 1 });

const Newsletter = mongoose.model("Newsletter", newsletterSchema);
const NewsletterSubscription = mongoose.model("NewsletterSubscription", subscriptionSchema);

module.exports = { Newsletter, NewsletterSubscription };
