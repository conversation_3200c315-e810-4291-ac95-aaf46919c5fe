const Forum = require("../models/Forum");
const Course = require("../models/Course");
const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");

// Get all forum topics for a course
exports.getForumTopics = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const { category, page = 1, limit = 10 } = req.query;

  // Check if course exists
  const course = await Course.findById(courseId);
  if (!course) {
    return next(new AppError("Course not found", 404));
  }

  const query = { course: courseId, isDeleted: false, activeStatus: 1 };
  if (category) query.category = category;

  const topics = await Forum.find(query)
    .populate("creator", "firstName lastName name profile.avatar role")
    .populate("posts.user", "firstName lastName name profile.avatar role")
    .sort({ isPinned: -1, lastActivity: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await Forum.countDocuments(query);

  res.status(200).json({
    status: "success",
    data: {
      topics,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit),
    },
  });
});

// Get a single forum topic with posts
exports.getForumTopic = catchAsync(async (req, res, next) => {
  const { topicId } = req.params;

  const topic = await Forum.findById(topicId)
    .populate("creator", "firstName lastName name profile.avatar role")
    .populate("posts.user", "firstName lastName name profile.avatar role")
    .populate("posts.replies.user", "firstName lastName name profile.avatar role");

  if (!topic || topic.isDeleted || topic.activeStatus === 0) {
    return next(new AppError("Forum topic not found", 404));
  }

  // Increment view count
  topic.viewCount += 1;
  await topic.save();

  res.status(200).json({
    status: "success",
    data: topic,
  });
});

// Create a new forum topic
exports.createForumTopic = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const { title, description, category, tags } = req.body;

  // Check if course exists
  const course = await Course.findById(courseId);
  if (!course) {
    return next(new AppError("Course not found", 404));
  }

  // Check if user is enrolled in the course or is the creator/admin
  const isEnrolled = course.enrolledUsers.some(
    (enrollment) => enrollment.user.toString() === req.user._id.toString()
  );
  const isCreator = course.creator.toString() === req.user._id.toString();
  const isAdmin = req.user.role === "admin";

  if (!isEnrolled && !isCreator && !isAdmin) {
    return next(new AppError("You must be enrolled in this course to create forum topics", 403));
  }

  const topic = await Forum.create({
    title,
    description,
    course: courseId,
    creator: req.user._id,
    category: category || "general",
    tags: tags || [],
  });

  await topic.populate("creator", "firstName lastName name profile.avatar role");

  res.status(201).json({
    status: "success",
    data: topic,
  });
});

// Add a post to a forum topic
exports.addForumPost = catchAsync(async (req, res, next) => {
  const { topicId } = req.params;
  const { content } = req.body;

  if (!content || !content.trim()) {
    return next(new AppError("Post content is required", 400));
  }

  const topic = await Forum.findById(topicId);
  if (!topic || topic.isDeleted || topic.activeStatus === 0) {
    return next(new AppError("Forum topic not found", 404));
  }

  if (topic.isLocked) {
    return next(new AppError("This topic is locked and cannot accept new posts", 403));
  }

  // Check if user has access to the course
  const course = await Course.findById(topic.course);
  const isEnrolled = course.enrolledUsers.some(
    (enrollment) => enrollment.user.toString() === req.user._id.toString()
  );
  const isCreator = course.creator.toString() === req.user._id.toString();
  const isAdmin = req.user.role === "admin";

  if (!isEnrolled && !isCreator && !isAdmin) {
    return next(new AppError("You must be enrolled in this course to post", 403));
  }

  topic.posts.push({
    user: req.user._id,
    content,
  });

  topic.lastActivity = new Date();
  await topic.save();

  await topic.populate("posts.user", "firstName lastName name profile.avatar role");

  res.status(201).json({
    status: "success",
    data: topic.posts[topic.posts.length - 1],
  });
});

// Vote on a post (upvote/downvote)
exports.voteOnPost = catchAsync(async (req, res, next) => {
  const { topicId, postId } = req.params;
  const { voteType } = req.body; // 'upvote' or 'downvote'

  if (!["upvote", "downvote"].includes(voteType)) {
    return next(new AppError("Invalid vote type", 400));
  }

  const topic = await Forum.findById(topicId);
  if (!topic || topic.isDeleted || topic.activeStatus === 0) {
    return next(new AppError("Forum topic not found", 404));
  }

  const post = topic.posts.id(postId);
  if (!post) {
    return next(new AppError("Post not found", 404));
  }

  const userId = req.user._id.toString();

  // Remove existing votes by this user
  post.upvotes = post.upvotes.filter((vote) => vote.user.toString() !== userId);
  post.downvotes = post.downvotes.filter((vote) => vote.user.toString() !== userId);

  // Add new vote
  if (voteType === "upvote") {
    post.upvotes.push({ user: req.user._id });
  } else {
    post.downvotes.push({ user: req.user._id });
  }

  await topic.save();

  res.status(200).json({
    status: "success",
    data: {
      upvotes: post.upvotes.length,
      downvotes: post.downvotes.length,
    },
  });
});

// Update forum topic (admin/creator only)
exports.updateForumTopic = catchAsync(async (req, res, next) => {
  const { topicId } = req.params;
  const { title, description, category, isPinned, isLocked, tags } = req.body;

  const topic = await Forum.findById(topicId);
  if (!topic || topic.isDeleted || topic.activeStatus === 0) {
    return next(new AppError("Forum topic not found", 404));
  }

  // Check permissions
  const isCreator = topic.creator.toString() === req.user._id.toString();
  const isAdmin = req.user.role === "admin";

  if (!isCreator && !isAdmin) {
    return next(new AppError("You are not authorized to update this topic", 403));
  }

  // Update fields
  if (title) topic.title = title;
  if (description) topic.description = description;
  if (category) topic.category = category;
  if (tags) topic.tags = tags;

  // Only admins can pin/lock topics
  if (isAdmin) {
    if (isPinned !== undefined) topic.isPinned = isPinned;
    if (isLocked !== undefined) topic.isLocked = isLocked;
  }

  await topic.save();

  res.status(200).json({
    status: "success",
    data: topic,
  });
});

// Delete forum topic (admin/creator only)
exports.deleteForumTopic = catchAsync(async (req, res, next) => {
  const { topicId } = req.params;

  const topic = await Forum.findById(topicId);
  if (!topic || topic.isDeleted || topic.activeStatus === 0) {
    return next(new AppError("Forum topic not found", 404));
  }

  // Check permissions
  const isCreator = topic.creator.toString() === req.user._id.toString();
  const isAdmin = req.user.role === "admin";

  if (!isCreator && !isAdmin) {
    return next(new AppError("You are not authorized to delete this topic", 403));
  }

  topic.isDeleted = true;
  topic.activeStatus = 0;
  await topic.save();

  res.status(200).json({
    status: "success",
    message: "Forum topic deleted successfully",
  });
});
