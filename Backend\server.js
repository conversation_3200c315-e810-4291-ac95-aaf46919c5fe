const dotenv = require("dotenv");
dotenv.config();
const express = require("express");
const connectDB = require("./config/db");
const fs = require("fs");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const mongoSanitize = require("express-mongo-sanitize");
const xss = require("xss-clean");
const http = require("http");
const https = require("https");
const path = require("path");
const seedDatabase = require("./utils/seeder");

const app = express();

// ==========================
// 🔧 Trust Proxy Configuration
// ==========================
// Configure trust proxy based on environment
if (process.env.NODE_ENV === 'production') {
  // In production, trust the first proxy (load balancer, CDN, etc.)
  app.set('trust proxy', 1);
} else {
  // In development, trust all proxies for testing
  app.set('trust proxy', true);
}
// Load SSL certificates (optional - only if SSL files exist)
let sslOptions = null;
try {
  if (fs.existsSync("./secret/key.pem") && fs.existsSync("./secret/crt.pem")) {
    sslOptions = {
      key: fs.readFileSync("./secret/key.pem"),
      cert: fs.readFileSync("./secret/crt.pem"),
      ca: fs.existsSync("./secret/ca.crt") ? fs.readFileSync("./secret/ca.crt") : undefined,
    };
    console.log("✅ SSL certificates loaded successfully");
  }
} catch (error) {
  console.log("⚠️ SSL certificates not found or invalid, using HTTP only");
}


// ==========================
// 📦 File Upload Handling
// ==========================
app.use(express.json({ limit: "1024mb" }));
app.use(express.urlencoded({ extended: true, limit: "1024mb" }));

// ==========================
// 🌐 Static Files
// ==========================
app.use(express.static(path.join(__dirname, "public")));
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// ==========================
// 🔐 Security Middlewares
// ==========================
app.use(helmet());

// Enhanced logging for production debugging
if (process.env.NODE_ENV === 'production') {
  app.use(morgan('combined'));

  // Debug middleware for proxy headers (only in production)
  app.use((req, res, next) => {
    if (process.env.DEBUG_PROXY === 'true') {
      console.log('🔍 Proxy Debug Info:', {
        ip: req.ip,
        ips: req.ips,
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-real-ip': req.headers['x-real-ip'],
        'cf-connecting-ip': req.headers['cf-connecting-ip'], // Cloudflare
        'x-forwarded-proto': req.headers['x-forwarded-proto'],
        remoteAddress: req.socket?.remoteAddress,
        method: req.method,
        url: req.url
      });
    }
    next();
  });
} else {
  app.use(morgan("dev"));
}

app.use(mongoSanitize());
app.use(xss());

// ==========================
// 🌍 CORS Configuration
// ==========================
const corsOptions = {
  origin: ["http://localhost:5173", "https://iim.thefabaf.com"],
  credentials: true,
};
app.use(cors(corsOptions));
app.options("*", cors(corsOptions)); // Allow preflight CORS requests



// ==========================
// 📦 Dynamic Routes
// ==========================
const routes = [
  "auth",
  "educator",
  "university",
  "admin",
  "quiz",
  "cms",
  "module",
  "moduleProgress",
  "certificate",
  "role",
  "staff",
  "blog",
  "user", // Added user routes
  "comment", // Added comment routes
  "notification", // Added notification routes
  "forum", // Added forum routes
  "newsletter", // Added newsletter routes
].reduce((acc, route) => {
  try {
    acc[route] = require(`./routes/${route}Routes`);
    if (typeof acc[route] !== "function") {
      throw new Error(`${route}Routes is not a valid Express router`);
    }
    app.use(`/api/${route}`, acc[route]);
  } catch (error) {
    console.error(`❌ Error loading ${route}Routes:`, error.message);
  }
  return acc;
}, {});

// ==========================
// ✅ Root Route
// ==========================
app.get("/", (req, res) => {
  res.send("✅ Backend API is running...");
});

// ==========================
// 🧯 Error Handling
// ==========================
const errorHandler = require("./middleware/errorHandler");
app.use(errorHandler);

app.all("*", (req, res, next) => {
  if (req.accepts("html")) {
    return res.status(404).sendFile(path.join(__dirname, "public", "404.html"));
  }
  if (req.accepts("json")) {
    const err = new Error(`Can't find ${req.originalUrl} on this server!`);
    err.status = "fail";
    err.statusCode = 404;
    return next(err);
  }
  res.status(404).send("404 Not Found");
});

// ==========================
// 🚀 Server Launch
// ==========================
const PORT = process.env.PORT || 5001;
const HTTPS_PORT = process.env.HTTPS_PORT || 5443;

// Create HTTP server
const httpServer = http.createServer(app);
httpServer.setTimeout(10 * 60 * 1000); // 10-minute timeout

// Create HTTPS server if SSL certificates are available
let httpsServer = null;
if (sslOptions) {
  httpsServer = https.createServer(sslOptions, app);
  httpsServer.setTimeout(10 * 60 * 1000); // 10-minute timeout
}

connectDB()
  .then(async () => {
    await seedDatabase();

    // Start HTTP server
    httpServer.listen(PORT, () => {
      console.log(`✅ HTTP Server running on port ${PORT}`);
    });

    // Start HTTPS server if SSL is available
    if (httpsServer) {
      httpsServer.listen(HTTPS_PORT, () => {
        console.log(`✅ HTTPS Server running on port ${HTTPS_PORT}`);
      });
    }
  })
  .catch((err) => {
    console.error("❌ MongoDB Connection Error:", err);
    process.exit(1);
  });
