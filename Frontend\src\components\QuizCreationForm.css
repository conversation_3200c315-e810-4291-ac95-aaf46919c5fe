.quiz-creation-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quiz-header-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.quiz-header-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.quiz-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.questions-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.add-question-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.add-question-btn:hover {
  background: #218838;
}

.question-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-number {
  font-weight: 600;
  color: #007bff;
  font-size: 16px;
}

.question-controls {
  display: flex;
  gap: 10px;
}

.toggle-btn {
  padding: 8px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-btn.active {
  background: #ffc107;
  color: #212529;
}

.toggle-btn:hover {
  opacity: 0.8;
}

.remove-question-btn {
  padding: 8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.remove-question-btn:hover {
  background: #c82333;
}

.options-section {
  margin-bottom: 20px;
}

.partial-credit-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.option-row input[type="text"] {
  flex: 1;
}

.option-row input[type="radio"] {
  width: auto;
  margin: 0;
}

.weight-input {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.weight-input input {
  width: 60px;
  padding: 6px;
  text-align: center;
}

.weight-input span {
  font-weight: 500;
  color: #666;
}

.question-footer {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 20px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #f0f0f0;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quiz-creation-form {
    padding: 15px;
    margin: 10px;
  }
  
  .quiz-settings {
    grid-template-columns: 1fr;
  }
  
  .question-footer {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .option-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .weight-input {
    justify-content: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
