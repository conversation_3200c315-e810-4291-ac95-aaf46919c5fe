import React, { useState } from 'react';
import { FaPlus, FaTrash, FaPercent, FaCog } from 'react-icons/fa';
import './QuizCreationForm.css';

const QuizCreationForm = ({ initialData = null, onSubmit, onCancel }) => {
  const [quizData, setQuizData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    timeLimit: initialData?.timeLimit || 30,
    passingScore: initialData?.passingScore || 60,
    questions: initialData?.questions || [
      {
        question: '',
        options: ['', '', '', ''],
        correctAnswer: '', // Legacy field
        correctAnswers: [], // New field for multiple correct answers with weights
        allowMultipleCorrect: false,
        explanation: '',
        points: 1
      }
    ]
  });

  const addQuestion = () => {
    setQuizData(prev => ({
      ...prev,
      questions: [
        ...prev.questions,
        {
          question: '',
          options: ['', '', '', ''],
          correctAnswer: '',
          correctAnswers: [],
          allowMultipleCorrect: false,
          explanation: '',
          points: 1
        }
      ]
    }));
  };

  const removeQuestion = (index) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const updateQuestion = (index, field, value) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === index ? { ...q, [field]: value } : q
      )
    }));
  };

  const updateOption = (questionIndex, optionIndex, value) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === questionIndex 
          ? { ...q, options: q.options.map((opt, j) => j === optionIndex ? value : opt) }
          : q
      )
    }));
  };

  const toggleMultipleCorrect = (questionIndex) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === questionIndex 
          ? { 
              ...q, 
              allowMultipleCorrect: !q.allowMultipleCorrect,
              correctAnswers: !q.allowMultipleCorrect ? [] : q.correctAnswers,
              correctAnswer: !q.allowMultipleCorrect ? '' : q.correctAnswer
            }
          : q
      )
    }));
  };

  const updateCorrectAnswer = (questionIndex, optionIndex, weight = 100) => {
    const question = quizData.questions[questionIndex];
    
    if (!question.allowMultipleCorrect) {
      // Single correct answer mode
      updateQuestion(questionIndex, 'correctAnswer', optionIndex.toString());
    } else {
      // Multiple correct answers mode
      const existingIndex = question.correctAnswers.findIndex(ca => ca.answer === optionIndex.toString());
      
      if (existingIndex >= 0) {
        // Update existing weight or remove if weight is 0
        const newCorrectAnswers = [...question.correctAnswers];
        if (weight > 0) {
          newCorrectAnswers[existingIndex].weight = weight;
        } else {
          newCorrectAnswers.splice(existingIndex, 1);
        }
        updateQuestion(questionIndex, 'correctAnswers', newCorrectAnswers);
      } else if (weight > 0) {
        // Add new correct answer
        updateQuestion(questionIndex, 'correctAnswers', [
          ...question.correctAnswers,
          { answer: optionIndex.toString(), weight }
        ]);
      }
    }
  };

  const getCorrectAnswerWeight = (questionIndex, optionIndex) => {
    const question = quizData.questions[questionIndex];
    
    if (!question.allowMultipleCorrect) {
      return question.correctAnswer === optionIndex.toString() ? 100 : 0;
    }
    
    const correctAnswer = question.correctAnswers.find(ca => ca.answer === optionIndex.toString());
    return correctAnswer ? correctAnswer.weight : 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate quiz data
    if (!quizData.title.trim()) {
      alert('Quiz title is required');
      return;
    }
    
    if (quizData.questions.length === 0) {
      alert('At least one question is required');
      return;
    }
    
    // Validate each question
    for (let i = 0; i < quizData.questions.length; i++) {
      const question = quizData.questions[i];
      
      if (!question.question.trim()) {
        alert(`Question ${i + 1} text is required`);
        return;
      }
      
      if (question.options.some(opt => !opt.trim())) {
        alert(`All options for question ${i + 1} are required`);
        return;
      }
      
      if (!question.allowMultipleCorrect && !question.correctAnswer) {
        alert(`Please select a correct answer for question ${i + 1}`);
        return;
      }
      
      if (question.allowMultipleCorrect && question.correctAnswers.length === 0) {
        alert(`Please set at least one correct answer with weight for question ${i + 1}`);
        return;
      }
    }
    
    onSubmit(quizData);
  };

  return (
    <div className="quiz-creation-form">
      <form onSubmit={handleSubmit}>
        <div className="quiz-header-section">
          <h2>{initialData ? 'Edit Quiz' : 'Create New Quiz'}</h2>
          
          <div className="form-group">
            <label>Quiz Title *</label>
            <input
              type="text"
              value={quizData.title}
              onChange={(e) => setQuizData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter quiz title"
              required
            />
          </div>
          
          <div className="form-group">
            <label>Description</label>
            <textarea
              value={quizData.description}
              onChange={(e) => setQuizData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter quiz description"
              rows="3"
            />
          </div>
          
          <div className="quiz-settings">
            <div className="form-group">
              <label>Time Limit (minutes)</label>
              <input
                type="number"
                value={quizData.timeLimit}
                onChange={(e) => setQuizData(prev => ({ ...prev, timeLimit: parseInt(e.target.value) }))}
                min="1"
                max="180"
              />
            </div>
            
            <div className="form-group">
              <label>Passing Score (%)</label>
              <input
                type="number"
                value={quizData.passingScore}
                onChange={(e) => setQuizData(prev => ({ ...prev, passingScore: parseInt(e.target.value) }))}
                min="1"
                max="100"
              />
            </div>
          </div>
        </div>

        <div className="questions-section">
          <div className="section-header">
            <h3>Questions</h3>
            <button type="button" onClick={addQuestion} className="add-question-btn">
              <FaPlus /> Add Question
            </button>
          </div>

          {quizData.questions.map((question, questionIndex) => (
            <div key={questionIndex} className="question-card">
              <div className="question-header">
                <span className="question-number">Question {questionIndex + 1}</span>
                <div className="question-controls">
                  <button
                    type="button"
                    onClick={() => toggleMultipleCorrect(questionIndex)}
                    className={`toggle-btn ${question.allowMultipleCorrect ? 'active' : ''}`}
                    title="Enable partial credit"
                  >
                    <FaPercent />
                  </button>
                  <button
                    type="button"
                    onClick={() => removeQuestion(questionIndex)}
                    className="remove-question-btn"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label>Question Text *</label>
                <textarea
                  value={question.question}
                  onChange={(e) => updateQuestion(questionIndex, 'question', e.target.value)}
                  placeholder="Enter your question"
                  rows="2"
                  required
                />
              </div>

              <div className="options-section">
                <label>Answer Options *</label>
                {question.allowMultipleCorrect && (
                  <p className="partial-credit-info">
                    <FaCog /> Partial Credit Mode: Set weight (0-100%) for each correct answer
                  </p>
                )}
                
                {question.options.map((option, optionIndex) => (
                  <div key={optionIndex} className="option-row">
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => updateOption(questionIndex, optionIndex, e.target.value)}
                      placeholder={`Option ${optionIndex + 1}`}
                      required
                    />
                    
                    {!question.allowMultipleCorrect ? (
                      <input
                        type="radio"
                        name={`correct-${questionIndex}`}
                        checked={question.correctAnswer === optionIndex.toString()}
                        onChange={() => updateCorrectAnswer(questionIndex, optionIndex)}
                      />
                    ) : (
                      <div className="weight-input">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={getCorrectAnswerWeight(questionIndex, optionIndex)}
                          onChange={(e) => updateCorrectAnswer(questionIndex, optionIndex, parseInt(e.target.value) || 0)}
                          placeholder="0"
                        />
                        <span>%</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="question-footer">
                <div className="form-group">
                  <label>Points</label>
                  <input
                    type="number"
                    value={question.points}
                    onChange={(e) => updateQuestion(questionIndex, 'points', parseInt(e.target.value) || 1)}
                    min="1"
                    max="10"
                  />
                </div>
                
                <div className="form-group">
                  <label>Explanation (Optional)</label>
                  <textarea
                    value={question.explanation}
                    onChange={(e) => updateQuestion(questionIndex, 'explanation', e.target.value)}
                    placeholder="Explain the correct answer"
                    rows="2"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="form-actions">
          <button type="button" onClick={onCancel} className="cancel-btn">
            Cancel
          </button>
          <button type="submit" className="submit-btn">
            {initialData ? 'Update Quiz' : 'Create Quiz'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default QuizCreationForm;
