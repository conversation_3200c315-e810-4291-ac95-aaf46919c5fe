.forum-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.forum-loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.forum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.forum-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.forum-icon {
  color: #007bff;
  font-size: 24px;
}

.forum-title h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.forum-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.category-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.create-topic-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.create-topic-btn:hover {
  background: #0056b3;
}

.create-topic-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.create-topic-form input,
.create-topic-form textarea,
.create-topic-form select {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.form-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.form-actions button[type="button"] {
  background: #6c757d;
  color: white;
}

.form-actions button[type="submit"] {
  background: #28a745;
  color: white;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.no-topics {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-topics svg {
  margin-bottom: 20px;
  opacity: 0.5;
}

.topic-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.topic-card.pinned {
  border-left: 4px solid #ffc107;
  background: #fffbf0;
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.topic-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.pin-icon,
.lock-icon {
  color: #ffc107;
  font-size: 14px;
}

.topic-stats {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.topic-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.topic-title {
  margin: 10px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.topic-description {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.topic-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.topic-tags {
  display: flex;
  gap: 8px;
}

.tag {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

/* Topic Detail Styles */
.topic-detail-header {
  margin-bottom: 30px;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-btn:hover {
  background: #5a6268;
}

.topic-info h1 {
  margin: 15px 0 10px 0;
  color: #333;
  font-size: 28px;
}

.topic-info p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.posts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.post-author strong {
  color: #333;
  margin-right: 10px;
}

.post-date {
  color: #666;
  font-size: 14px;
}

.post-votes {
  display: flex;
  gap: 10px;
}

.vote-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.vote-btn.upvote:hover {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.vote-btn.downvote:hover {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.post-content {
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}

.add-post-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.add-post-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  resize: vertical;
}

.post-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.post-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.post-btn:not(:disabled):hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .forum-container {
    padding: 15px;
  }
  
  .forum-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .forum-controls {
    justify-content: space-between;
  }
  
  .topic-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .topic-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .post-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
