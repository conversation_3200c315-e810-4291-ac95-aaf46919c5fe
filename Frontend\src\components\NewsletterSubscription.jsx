import React, { useState, useEffect } from 'react';
import { FaEnvelope, FaCheck, FaTimes, FaNewspaper, FaEye } from 'react-icons/fa';
import './NewsletterSubscription.css';

const NewsletterSubscription = ({ showPreviousNewsletters = true }) => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [newsletters, setNewsletters] = useState([]);
  const [showNewsletters, setShowNewsletters] = useState(false);

  useEffect(() => {
    if (showPreviousNewsletters) {
      fetchNewsletters();
    }
  }, [showPreviousNewsletters]);

  const fetchNewsletters = async () => {
    try {
      const response = await fetch('/api/newsletter?limit=5');
      if (response.ok) {
        const data = await response.json();
        setNewsletters(data.data.newsletters);
      }
    } catch (error) {
      console.error('Error fetching newsletters:', error);
    }
  };

  const handleSubscribe = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setMessage('Email is required');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          firstName,
          lastName,
          preferences: {
            frequency: 'weekly',
            categories: ['general', 'courses', 'announcements']
          }
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubscribed(true);
        setMessage('Successfully subscribed to our newsletter!');
        setEmail('');
        setFirstName('');
        setLastName('');
      } else {
        setMessage(data.message || 'Subscription failed. Please try again.');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="newsletter-subscription">
      {!isSubscribed ? (
        <div className="subscription-form-container">
          <div className="subscription-header">
            <FaEnvelope className="newsletter-icon" />
            <h3>Stay Updated</h3>
            <p>Subscribe to our newsletter for the latest courses, updates, and educational content.</p>
          </div>

          <form onSubmit={handleSubscribe} className="subscription-form">
            <div className="form-row">
              <input
                type="text"
                placeholder="First Name (Optional)"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="name-input"
              />
              <input
                type="text"
                placeholder="Last Name (Optional)"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="name-input"
              />
            </div>
            
            <div className="email-row">
              <input
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="email-input"
              />
              <button 
                type="submit" 
                disabled={loading}
                className="subscribe-btn"
              >
                {loading ? 'Subscribing...' : 'Subscribe'}
              </button>
            </div>

            {message && (
              <div className={`message ${message.includes('Successfully') ? 'success' : 'error'}`}>
                {message.includes('Successfully') ? <FaCheck /> : <FaTimes />}
                {message}
              </div>
            )}
          </form>

          <div className="subscription-benefits">
            <h4>What you'll get:</h4>
            <ul>
              <li>📚 Latest course announcements</li>
              <li>🎓 Educational tips and resources</li>
              <li>📰 Weekly digest of new content</li>
              <li>🎯 Personalized learning recommendations</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="subscription-success">
          <FaCheck className="success-icon" />
          <h3>Thank you for subscribing!</h3>
          <p>You'll receive our newsletter with the latest updates and educational content.</p>
        </div>
      )}

      {showPreviousNewsletters && newsletters.length > 0 && (
        <div className="previous-newsletters">
          <div className="newsletters-header">
            <h4>
              <FaNewspaper /> Previous Newsletters
            </h4>
            <button 
              onClick={() => setShowNewsletters(!showNewsletters)}
              className="toggle-newsletters-btn"
            >
              {showNewsletters ? 'Hide' : 'Show'} ({newsletters.length})
            </button>
          </div>

          {showNewsletters && (
            <div className="newsletters-list">
              {newsletters.map(newsletter => (
                <div key={newsletter._id} className="newsletter-item">
                  <div className="newsletter-content">
                    <h5>{newsletter.title}</h5>
                    <p>{newsletter.excerpt}</p>
                    <div className="newsletter-meta">
                      <span className="newsletter-date">
                        {formatDate(newsletter.sentAt)}
                      </span>
                      {newsletter.tags && newsletter.tags.length > 0 && (
                        <div className="newsletter-tags">
                          {newsletter.tags.map((tag, index) => (
                            <span key={index} className="tag">{tag}</span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <button 
                    onClick={() => window.open(`/newsletter/${newsletter._id}`, '_blank')}
                    className="view-newsletter-btn"
                    title="View Newsletter"
                  >
                    <FaEye />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="subscription-footer">
        <p>
          <small>
            We respect your privacy. Unsubscribe at any time. 
            <a href="/privacy-policy" target="_blank" rel="noopener noreferrer">
              Privacy Policy
            </a>
          </small>
        </p>
      </div>
    </div>
  );
};

export default NewsletterSubscription;
