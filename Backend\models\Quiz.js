const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  course: { type: mongoose.Schema.Types.ObjectId, ref: 'Course', required: true },
  questions: [{
    question: { type: String, required: true },
    options: [{ type: String, required: true }],
    // Enhanced to support multiple correct answers with weights
    correctAnswers: [{
      answer: { type: String, required: true }, // The answer option
      weight: { type: Number, required: true, min: 0, max: 100 } // Percentage weight (0-100)
    }],
    // Keep legacy field for backward compatibility
    correctAnswer: { type: String },
    explanation: { type: String },
    points: { type: Number, default: 1 },
    // Flag to indicate if question supports multiple correct answers
    allowMultipleCorrect: { type: Boolean, default: false }
  }],
  timeLimit: { type: Number }, // in minutes
  passingScore: { type: Number, default: 60 }, // percentage - can be customized per quiz
  attempts: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    score: { type: Number }, // Raw score points
    percentage: { type: Number }, // Calculated percentage
    answers: [{
      questionIndex: { type: Number },
      questionId: { type: mongoose.Schema.Types.ObjectId },
      selectedAnswer: { type: String },
      selectedAnswers: [{ type: String }], // For multiple selection questions
      isCorrect: { type: Boolean },
      partialScore: { type: Number, default: 0 }, // Partial credit earned
      maxScore: { type: Number, default: 1 } // Maximum possible score for this question
    }],
    completedAt: { type: Date, default: Date.now }
  }]
}, { timestamps: true });

module.exports = mongoose.model('Quiz', quizSchema);