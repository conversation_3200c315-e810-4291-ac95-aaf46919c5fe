const Quiz = require('../models/Quiz');
const Course = require('../models/Course');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

// Create a new quiz
exports.createQuiz = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const course = await Course.findById(courseId);

  if (!course) {
    return next(new AppError('Course not found', 404));
  }

  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to create quiz for this course', 403));
  }

  const quiz = await Quiz.create({
    ...req.body,
    course: courseId
  });

  course.quizzes.push(quiz._id);
  await course.save();

  res.status(201).json({
    status: 'success',
    data: quiz
  });
});

// Get a quiz
exports.getQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('course', 'title');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: quiz
  });
});

// Update a quiz
exports.updateQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update this quiz', 403));
  }

  const updatedQuiz = await Quiz.findByIdAndUpdate(
    req.params.quizId,
    req.body,
    { new: true, runValidators: true }
  );

  res.status(200).json({
    status: 'success',
    data: updatedQuiz
  });
});

// Delete a quiz
exports.deleteQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to delete this quiz', 403));
  }

  await Quiz.findByIdAndDelete(req.params.quizId);

  // Remove quiz reference from course
  course.quizzes = course.quizzes.filter(q => q.toString() !== quiz._id.toString());
  await course.save();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Submit quiz attempt with partial credit support
exports.submitQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const { answers } = req.body;
  let totalScore = 0;
  const totalPoints = quiz.questions.reduce((acc, q) => acc + (q.points || 1), 0);

  // Enhanced evaluation with partial credit support
  let evaluatedAnswers = [];

  if (Array.isArray(answers)) {
    // Handle array format (legacy)
    evaluatedAnswers = answers.map((answer, index) => {
      const question = quiz.questions[index];
      const result = evaluateQuestionAnswer(question, answer);

      totalScore += result.partialScore;

      return {
        questionIndex: index,
        questionId: question._id,
        selectedAnswer: answer,
        correctAnswer: question.correctAnswer,
        isCorrect: result.isCorrect,
        partialScore: result.partialScore,
        maxScore: question.points || 1
      };
    });
  } else {
    // Handle object format with question IDs as keys
    evaluatedAnswers = quiz.questions.map((question, index) => {
      const questionId = question._id.toString();
      const selectedAnswer = answers[questionId];
      const result = evaluateQuestionAnswer(question, selectedAnswer);

      totalScore += result.partialScore;

      return {
        questionIndex: index,
        questionId: questionId,
        selectedAnswer: selectedAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect: result.isCorrect,
        partialScore: result.partialScore,
        maxScore: question.points || 1
      };
    });
  }

  const finalPercentage = Math.round((totalScore / totalPoints) * 100);
  const passed = finalPercentage >= quiz.passingScore;

  // Save the attempt
  quiz.attempts.push({
    user: req.user._id,
    score: totalScore,
    percentage: finalPercentage,
    answers: evaluatedAnswers,
    completedAt: new Date()
  });

  await quiz.save();

  // Return detailed results including correct answers and partial scores
  res.status(200).json({
    status: 'success',
    data: {
      score: totalScore,
      totalPoints,
      percentage: finalPercentage,
      passed,
      answers: evaluatedAnswers,
      questions: quiz.questions.map(q => ({
        id: q._id,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer, // Legacy field
        correctAnswers: q.correctAnswers, // New field with weights
        allowMultipleCorrect: q.allowMultipleCorrect,
        points: q.points || 1
      }))
    }
  });
});

// Get quiz results
exports.getQuizResults = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('attempts.user', 'name email');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const userAttempt = quiz.attempts.find(
    attempt => attempt.user._id.toString() === req.user._id.toString()
  );

  if (!userAttempt) {
    return next(new AppError('No attempt found for this quiz', 404));
  }

  res.status(200).json({
    status: 'success',
    data: userAttempt
  });
});

// Helper function to evaluate question answers with partial credit
function evaluateQuestionAnswer(question, selectedAnswer) {
  const questionPoints = question.points || 1;

  // Handle legacy single correct answer format
  if (!question.allowMultipleCorrect || !question.correctAnswers || question.correctAnswers.length === 0) {
    const isCorrect = selectedAnswer === question.correctAnswer;
    return {
      isCorrect,
      partialScore: isCorrect ? questionPoints : 0
    };
  }

  // Handle new multiple correct answers with weights
  const correctAnswerObj = question.correctAnswers.find(ca => ca.answer === selectedAnswer);

  if (correctAnswerObj) {
    const partialScore = (correctAnswerObj.weight / 100) * questionPoints;
    return {
      isCorrect: correctAnswerObj.weight === 100,
      partialScore: Math.round(partialScore * 100) / 100 // Round to 2 decimal places
    };
  }

  return {
    isCorrect: false,
    partialScore: 0
  };
}