const express = require("express");
const router = express.Router();
const newsletterController = require("../controllers/newsletterController");
const { protect, restrictTo, hasPermission } = require("../middleware/auth");
const { PERMISSIONS } = require("../utils/permissions");
const upload = require("../middleware/uploads");

// Public routes
router.post("/subscribe", newsletterController.subscribe);
router.post("/unsubscribe", newsletterController.unsubscribe);
router.get("/", newsletterController.getNewsletters);
router.get("/:id", newsletterController.getNewsletter);

// Protected admin routes
router.use(protect);

// Admin newsletter management
router.post(
  "/admin",
  restrictTo("admin"),
  hasPermission(PERMISSIONS.BLOG_MANAGEMENT.CREATE_BLOG), // Reusing blog permission for now
  upload.single("coverImage"),
  newsletterController.createNewsletter
);

router.get(
  "/admin/all",
  restrictTo("admin"),
  hasPermission(PERMISSIONS.BLOG_MANAGEMENT.VIEW_BLOGS),
  newsletterController.getAllNewsletters
);

router.put(
  "/admin/:id",
  restrictTo("admin"),
  hasPermission(PERMISSIONS.BLOG_MANAGEMENT.EDIT_BLOG),
  upload.single("coverImage"),
  newsletterController.updateNewsletter
);

router.delete(
  "/admin/:id",
  restrictTo("admin"),
  hasPermission(PERMISSIONS.BLOG_MANAGEMENT.DELETE_BLOG),
  newsletterController.deleteNewsletter
);

// Subscription management
router.get(
  "/admin/subscriptions/stats",
  restrictTo("admin"),
  newsletterController.getSubscriptionStats
);

router.get(
  "/admin/subscriptions",
  restrictTo("admin"),
  newsletterController.getSubscriptions
);

module.exports = router;
