import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes, FaTag } from 'react-icons/fa';
import './BlogSearchWithSuggestions.css';

const BlogSearchWithSuggestions = ({ 
  searchTerm, 
  onSearchChange, 
  tagFilter, 
  onTagFilterChange,
  statusFilter,
  onStatusFilterChange,
  sortBy,
  onSortByChange,
  onCreateBlog 
}) => {
  const [tagSuggestions, setTagSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm && searchTerm.length > 1) {
        fetchTagSuggestions(searchTerm);
      } else {
        fetchTagSuggestions(); // Get popular tags
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchRef.current && 
        !searchRef.current.contains(event.target) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchTagSuggestions = async (search = '') => {
    try {
      setLoading(true);
      const response = await fetch(`/api/blog/tags/suggestions?search=${encodeURIComponent(search)}`);
      
      if (response.ok) {
        const data = await response.json();
        setTagSuggestions(data.tags || []);
      }
    } catch (error) {
      console.error('Error fetching tag suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchFocus = () => {
    setSearchFocused(true);
    setShowSuggestions(true);
    if (tagSuggestions.length === 0) {
      fetchTagSuggestions();
    }
  };

  const handleSearchBlur = () => {
    setSearchFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      if (!searchFocused) {
        setShowSuggestions(false);
      }
    }, 200);
  };

  const handleTagSelect = (tag) => {
    onTagFilterChange(tag);
    setShowSuggestions(false);
  };

  const handleSearchSelect = (tag) => {
    onSearchChange(tag);
    setShowSuggestions(false);
  };

  const clearSearch = () => {
    onSearchChange('');
    searchRef.current?.focus();
  };

  const clearTagFilter = () => {
    onTagFilterChange('');
  };

  return (
    <div className="enhanced-blog-search">
      <div className="search-filter-row">
        {/* Enhanced Search Input with Suggestions */}
        <div className="search-input-container" ref={searchRef}>
          <div className="search-input-wrapper">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search blogs by title, content, or tags..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              onFocus={handleSearchFocus}
              onBlur={handleSearchBlur}
              className="enhanced-search-input"
            />
            {searchTerm && (
              <button onClick={clearSearch} className="clear-search-btn">
                <FaTimes />
              </button>
            )}
          </div>

          {/* Tag Suggestions Dropdown */}
          {showSuggestions && (
            <div className="suggestions-dropdown" ref={suggestionsRef}>
              {loading ? (
                <div className="suggestion-loading">Loading suggestions...</div>
              ) : tagSuggestions.length > 0 ? (
                <>
                  <div className="suggestions-header">
                    <span>Popular Tags</span>
                  </div>
                  <div className="suggestions-list">
                    {tagSuggestions.map((suggestion, index) => (
                      <div key={index} className="suggestion-item">
                        <button
                          onClick={() => handleSearchSelect(suggestion.tag)}
                          className="suggestion-search-btn"
                          title="Search for this tag"
                        >
                          <FaSearch />
                        </button>
                        <button
                          onClick={() => handleTagSelect(suggestion.tag)}
                          className="suggestion-tag-btn"
                          title="Filter by this tag"
                        >
                          <FaTag />
                          <span className="tag-name">{suggestion.tag}</span>
                          <span className="tag-count">({suggestion.count})</span>
                        </button>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="no-suggestions">No tag suggestions found</div>
              )}
            </div>
          )}
        </div>

        {/* Filter Controls */}
        <div className="filter-controls">
          {/* Tag Filter with Clear Option */}
          <div className="tag-filter-container">
            <select
              className="filter-select"
              value={tagFilter}
              onChange={(e) => onTagFilterChange(e.target.value)}
            >
              <option value="">All Tags</option>
              {tagSuggestions.map((suggestion, index) => (
                <option key={index} value={suggestion.tag}>
                  {suggestion.tag} ({suggestion.count})
                </option>
              ))}
            </select>
            {tagFilter && (
              <button onClick={clearTagFilter} className="clear-filter-btn">
                <FaTimes />
              </button>
            )}
          </div>

          {/* Status Filter */}
          <select
            className="filter-select"
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value)}
          >
            <option value="">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
          </select>

          {/* Sort Options */}
          <select
            className="filter-select"
            value={sortBy}
            onChange={(e) => onSortByChange(e.target.value)}
          >
            <option value="">Sort By</option>
            <option value="title-asc">Title (A-Z)</option>
            <option value="title-desc">Title (Z-A)</option>
            <option value="date-asc">Date (Oldest first)</option>
            <option value="date-desc">Date (Newest first)</option>
            <option value="status">Status</option>
          </select>

          {/* Create Blog Button */}
          <button className="add-blog-btn" onClick={onCreateBlog}>
            <FaPlus /> Add Blog
          </button>
        </div>
      </div>

      {/* Active Filters Display */}
      {(searchTerm || tagFilter || statusFilter) && (
        <div className="active-filters">
          <span className="filters-label">Active filters:</span>
          {searchTerm && (
            <span className="filter-tag">
              Search: "{searchTerm}"
              <button onClick={clearSearch}><FaTimes /></button>
            </span>
          )}
          {tagFilter && (
            <span className="filter-tag">
              Tag: {tagFilter}
              <button onClick={clearTagFilter}><FaTimes /></button>
            </span>
          )}
          {statusFilter && (
            <span className="filter-tag">
              Status: {statusFilter}
              <button onClick={() => onStatusFilterChange('')}><FaTimes /></button>
            </span>
          )}
          <button 
            className="clear-all-filters"
            onClick={() => {
              onSearchChange('');
              onTagFilterChange('');
              onStatusFilterChange('');
              onSortByChange('');
            }}
          >
            Clear All
          </button>
        </div>
      )}
    </div>
  );
};

export default BlogSearchWithSuggestions;
