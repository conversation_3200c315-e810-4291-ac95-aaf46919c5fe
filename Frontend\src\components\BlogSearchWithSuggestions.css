.enhanced-blog-search {
  margin-bottom: 24px;
}

.search-filter-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.search-input-container {
  position: relative;
  flex: 2;
  min-width: 300px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.enhanced-search-input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
  background: white;
}

.enhanced-search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6b7280;
  font-size: 16px;
  z-index: 1;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.suggestions-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.suggestions-list {
  padding: 8px 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 0 8px;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background: #f9fafb;
}

.suggestion-search-btn,
.suggestion-tag-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-search-btn {
  color: #007bff;
  margin-right: 4px;
}

.suggestion-search-btn:hover {
  background: #e3f2fd;
}

.suggestion-tag-btn {
  flex: 1;
  justify-content: flex-start;
  color: #374151;
}

.suggestion-tag-btn:hover {
  background: #f3f4f6;
}

.tag-name {
  font-weight: 500;
}

.tag-count {
  color: #6b7280;
  font-size: 12px;
}

.suggestion-loading,
.no-suggestions {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.tag-filter-container {
  position: relative;
  display: flex;
  align-items: center;
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease;
  min-width: 120px;
}

.filter-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.clear-filter-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  font-size: 12px;
}

.clear-filter-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.add-blog-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.add-blog-btn:hover {
  background: #0056b3;
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.filters-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.filter-tag button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  font-size: 10px;
  transition: all 0.2s ease;
}

.filter-tag button:hover {
  background: #f3f4f6;
  color: #374151;
}

.clear-all-filters {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-all-filters:hover {
  background: #c82333;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-filter-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-input-container {
    min-width: auto;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-select,
  .add-blog-btn {
    width: 100%;
  }
  
  .active-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filter-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }
}

@media (max-width: 480px) {
  .enhanced-search-input {
    padding: 10px 12px 10px 36px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .search-icon {
    left: 10px;
  }
  
  .suggestions-dropdown {
    max-height: 200px;
  }
}
