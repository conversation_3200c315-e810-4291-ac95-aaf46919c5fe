const Blog = require("../models/Blog");

// Create
exports.createBlog = async (req, res) => {
  try {
    // Get fields from FormData
    const title = req.body.title;
    const shortDescription = req.body.shortDescription;
    const content = req.body.content;
    const tags = req.body.tags;
    const status = req.body.status;
    const slug = title.toLowerCase().replace(/ /g, "-") + "-" + Date.now(); // simple slug

    // Parse tags if it's a string (from FormData)
    let parsedTags = tags;
    if (typeof tags === 'string') {
      try {
        // Check if it's a JSON string array
        if (tags.startsWith('[') && tags.endsWith(']')) {
          parsedTags = JSON.parse(tags);
        } else {
          // If it's a comma-separated string
          parsedTags = tags.split(',').map(tag => tag.trim());
        }
      } catch (err) {
        console.error('Error parsing tags:', err);
        parsedTags = [tags]; // Fallback to single tag
      }
    }

    // Handle file upload
    let coverImagePath = null;
    if (req.file) {
      // The file has been uploaded to uploads/blogs directory
      // The path should be relative to the public directory
      coverImagePath = `uploads/blogs/${req.file.filename}`;
    }

    const blog = new Blog({
      title,
      slug,
      shortDescription,
      content,
      tags: parsedTags,
      status,
      createdBy: req.user._id,
      coverImage: coverImagePath, // This will be null if no image was uploaded
    });

    await blog.save();
    res.status(201).json({ success: true, blog });
  } catch (error) {
    console.error('Error creating blog:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Read all (with enhanced filters like status, tags, search)
exports.getAllBlogs = async (req, res) => {
  try {
    const { status, tag, page = 1, limit = 10, search, sortBy } = req.query;
    const query = { isDeleted: false, activeStatus: 1 };

    if (status) query.status = status;
    if (tag) query.tags = { $in: [tag] }; // Support multiple tags

    // Enhanced search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { shortDescription: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } }
      ];
    }

    // Enhanced sorting options
    let sortOptions = { createdAt: -1 }; // Default sort
    if (sortBy) {
      switch (sortBy) {
        case 'title-asc':
          sortOptions = { title: 1 };
          break;
        case 'title-desc':
          sortOptions = { title: -1 };
          break;
        case 'date-asc':
          sortOptions = { createdAt: 1 };
          break;
        case 'date-desc':
          sortOptions = { createdAt: -1 };
          break;
        case 'status':
          sortOptions = { status: 1, createdAt: -1 };
          break;
      }
    }

    const blogs = await Blog.find(query)
      .populate({
        path: 'createdBy',
        select: 'firstName lastName name email profile.avatar role'
      })
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const total = await Blog.countDocuments(query);

    res.json({
      success: true,
      blogs,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Read single
exports.getBlogById = async (req, res) => {
  try {
    const blog = await Blog.findOne({
      _id: req.params.id,
      isDeleted: false,
      activeStatus: 1
    }).populate({
      path: 'createdBy',
      select: 'name email profile.avatar role'
    });

    if (!blog)
      return res
        .status(404)
        .json({ success: false, message: "Blog not found" });

    res.json({ success: true, blog });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Update
exports.updateBlog = async (req, res) => {
  try {
    // Get fields from FormData
    const title = req.body.title;
    const shortDescription = req.body.shortDescription;
    const content = req.body.content;
    const tags = req.body.tags;
    const status = req.body.status;

    const blog = await Blog.findById(req.params.id);

    if (!blog || blog.isDeleted || blog.activeStatus === 0)
      return res
        .status(404)
        .json({ success: false, message: "Blog not found" });

    // Update basic fields if provided
    if (title) blog.title = title;
    if (shortDescription) blog.shortDescription = shortDescription;
    if (content) blog.content = content;
    if (status) blog.status = status;

    // Parse tags if it's a string (from FormData)
    if (tags) {
      let parsedTags = tags;
      if (typeof tags === 'string') {
        try {
          // Check if it's a JSON string array
          if (tags.startsWith('[') && tags.endsWith(']')) {
            parsedTags = JSON.parse(tags);
          } else {
            // If it's a comma-separated string
            parsedTags = tags.split(',').map(tag => tag.trim());
          }
        } catch (err) {
          console.error('Error parsing tags:', err);
          parsedTags = [tags]; // Fallback to single tag
        }
      }
      blog.tags = parsedTags;
    }

    // Handle file upload
    if (req.file) {
      // The file has been uploaded to uploads/blogs directory
      // The path should be relative to the public directory
      blog.coverImage = `uploads/blogs/${req.file.filename}`;
    }

    await blog.save();
    res.json({ success: true, blog });
  } catch (error) {
    console.error('Error updating blog:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

// Delete (soft delete)
exports.deleteBlog = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);

    if (!blog)
      return res
        .status(404)
        .json({ success: false, message: "Blog not found" });

    blog.isDeleted = true;
    blog.activeStatus = 0;
    await blog.save();

    res.json({ success: true, message: "Blog deleted" });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Restore a deleted blog
exports.restoreBlog = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);

    if (!blog)
      return res
        .status(404)
        .json({ success: false, message: "Blog not found" });

    blog.isDeleted = false;
    blog.activeStatus = 1;
    await blog.save();

    res.json({ success: true, message: "Blog restored", blog });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get all deleted blogs (admin function)
exports.getDeletedBlogs = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const query = {
      $or: [
        { isDeleted: true },
        { activeStatus: 0 }
      ]
    };

    const blogs = await Blog.find(query)
      .populate({
        path: 'createdBy',
        select: 'name email profile.avatar role'
      })
      .sort({ updatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const total = await Blog.countDocuments(query);

    res.json({ success: true, blogs, total });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get tag suggestions for auto-complete
exports.getTagSuggestions = async (req, res) => {
  try {
    const { search } = req.query;

    // Get all unique tags from published blogs
    const pipeline = [
      { $match: { isDeleted: false, activeStatus: 1, status: 'published' } },
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1, _id: 1 } }
    ];

    // Add search filter if provided
    if (search) {
      pipeline.splice(2, 0, { $match: { tags: { $regex: search, $options: 'i' } } });
    }

    const tagAggregation = await Blog.aggregate(pipeline);

    const tags = tagAggregation.map(item => ({
      tag: item._id,
      count: item.count
    }));

    res.json({
      success: true,
      tags: tags.slice(0, 20) // Limit to top 20 suggestions
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get blog statistics
exports.getBlogStats = async (req, res) => {
  try {
    const totalBlogs = await Blog.countDocuments({ isDeleted: false, activeStatus: 1 });
    const publishedBlogs = await Blog.countDocuments({
      isDeleted: false,
      activeStatus: 1,
      status: 'published'
    });
    const draftBlogs = await Blog.countDocuments({
      isDeleted: false,
      activeStatus: 1,
      status: 'draft'
    });

    // Get most popular tags
    const popularTags = await Blog.aggregate([
      { $match: { isDeleted: false, activeStatus: 1, status: 'published' } },
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      stats: {
        totalBlogs,
        publishedBlogs,
        draftBlogs,
        popularTags: popularTags.map(tag => ({
          name: tag._id,
          count: tag.count
        }))
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};
