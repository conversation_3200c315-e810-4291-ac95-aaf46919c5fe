.newsletter-subscription {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.subscription-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.subscription-header {
  text-align: center;
  margin-bottom: 30px;
}

.newsletter-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.subscription-header h3 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.subscription-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
}

.subscription-form {
  margin-bottom: 30px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.name-input {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
}

.name-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.email-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.email-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
}

.email-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.subscribe-btn {
  padding: 12px 24px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.subscribe-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.subscribe-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 15px;
}

.message.success {
  background: rgba(40, 167, 69, 0.2);
  border: 1px solid rgba(40, 167, 69, 0.3);
  color: #d4edda;
}

.message.error {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #f8d7da;
}

.subscription-benefits {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.subscription-benefits h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.subscription-benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.subscription-benefits li {
  padding: 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.subscription-success {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  font-size: 64px;
  color: #28a745;
  margin-bottom: 20px;
}

.subscription-success h3 {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 600;
}

.subscription-success p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.previous-newsletters {
  margin-top: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.newsletters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.newsletters-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.toggle-newsletters-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.toggle-newsletters-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.newsletters-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.newsletter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.newsletter-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.newsletter-content {
  flex: 1;
}

.newsletter-content h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.newsletter-content p {
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

.newsletter-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.newsletter-date {
  font-size: 12px;
  opacity: 0.7;
}

.newsletter-tags {
  display: flex;
  gap: 6px;
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.view-newsletter-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 15px;
}

.view-newsletter-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.subscription-footer {
  margin-top: 20px;
  text-align: center;
  opacity: 0.7;
}

.subscription-footer small {
  font-size: 12px;
}

.subscription-footer a {
  color: white;
  text-decoration: underline;
  margin-left: 5px;
}

.subscription-footer a:hover {
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .newsletter-subscription {
    padding: 20px;
    margin: 15px 0;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .email-row {
    flex-direction: column;
  }
  
  .newsletter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .view-newsletter-btn {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .newsletter-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .newsletters-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
