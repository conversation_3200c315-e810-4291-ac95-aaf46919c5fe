const mongoose = require("mongoose");

const forumSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Course",
      required: true,
    },
    creator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    category: {
      type: String,
      enum: ["general", "qa", "discussion", "announcement"],
      default: "general",
    },
    isPinned: { type: Boolean, default: false },
    isLocked: { type: Boolean, default: false },
    tags: [{ type: String }],
    posts: [
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
        content: { type: String, required: true },
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now },
        upvotes: [
          {
            user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
            createdAt: { type: Date, default: Date.now },
          },
        ],
        downvotes: [
          {
            user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
            createdAt: { type: Date, default: Date.now },
          },
        ],
        replies: [
          {
            user: {
              type: mongoose.Schema.Types.ObjectId,
              ref: "User",
              required: true,
            },
            content: { type: String, required: true },
            createdAt: { type: Date, default: Date.now },
            updatedAt: { type: Date, default: Date.now },
            upvotes: [
              {
                user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
                createdAt: { type: Date, default: Date.now },
              },
            ],
            downvotes: [
              {
                user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
                createdAt: { type: Date, default: Date.now },
              },
            ],
          },
        ],
      },
    ],
    viewCount: { type: Number, default: 0 },
    lastActivity: { type: Date, default: Date.now },
    isDeleted: { type: Boolean, default: false },
    activeStatus: { type: Number, default: 1 }, // 1: active, 0: inactive
  },
  { timestamps: true }
);

// Index for better performance
forumSchema.index({ course: 1, createdAt: -1 });
forumSchema.index({ category: 1, isPinned: -1, lastActivity: -1 });

module.exports = mongoose.model("Forum", forumSchema);
