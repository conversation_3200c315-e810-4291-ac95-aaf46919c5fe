const express = require("express");
const router = express.Router();
const forumController = require("../controllers/forumController");
const { protect, restrictTo } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(protect);

// Forum topic routes
router.get("/course/:courseId", forumController.getForumTopics);
router.post("/course/:courseId", forumController.createForumTopic);
router.get("/topic/:topicId", forumController.getForumTopic);
router.put("/topic/:topicId", forumController.updateForumTopic);
router.delete("/topic/:topicId", forumController.deleteForumTopic);

// Forum post routes
router.post("/topic/:topicId/posts", forumController.addForumPost);
router.post("/topic/:topicId/posts/:postId/vote", forumController.voteOnPost);

module.exports = router;
