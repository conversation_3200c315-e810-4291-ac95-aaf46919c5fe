const { Newsletter, NewsletterSubscription } = require("../models/Newsletter");
const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");
const crypto = require("crypto");

// Subscribe to newsletter
exports.subscribe = catchAsync(async (req, res, next) => {
  const { email, firstName, lastName, preferences } = req.body;

  if (!email) {
    return next(new AppError("Email is required", 400));
  }

  // Check if already subscribed
  let subscription = await NewsletterSubscription.findOne({ email });

  if (subscription) {
    if (subscription.isActive) {
      return next(new AppError("Email is already subscribed", 400));
    } else {
      // Reactivate subscription
      subscription.isActive = true;
      subscription.subscribedAt = new Date();
      subscription.unsubscribedAt = undefined;
      if (firstName) subscription.firstName = firstName;
      if (lastName) subscription.lastName = lastName;
      if (preferences) subscription.preferences = { ...subscription.preferences, ...preferences };
    }
  } else {
    // Create new subscription
    const verificationToken = crypto.randomBytes(32).toString("hex");
    const unsubscribeToken = crypto.randomBytes(32).toString("hex");

    subscription = new NewsletterSubscription({
      email,
      firstName,
      lastName,
      preferences: preferences || {},
      verificationToken,
      unsubscribeToken,
    });
  }

  await subscription.save();

  res.status(201).json({
    status: "success",
    message: "Successfully subscribed to newsletter",
    data: {
      email: subscription.email,
      subscribedAt: subscription.subscribedAt,
    },
  });
});

// Unsubscribe from newsletter
exports.unsubscribe = catchAsync(async (req, res, next) => {
  const { token, email } = req.body;

  let subscription;

  if (token) {
    subscription = await NewsletterSubscription.findOne({ unsubscribeToken: token });
  } else if (email) {
    subscription = await NewsletterSubscription.findOne({ email });
  } else {
    return next(new AppError("Token or email is required", 400));
  }

  if (!subscription) {
    return next(new AppError("Subscription not found", 404));
  }

  subscription.isActive = false;
  subscription.unsubscribedAt = new Date();
  await subscription.save();

  res.status(200).json({
    status: "success",
    message: "Successfully unsubscribed from newsletter",
  });
});

// Get all newsletters (public - for display)
exports.getNewsletters = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, tag } = req.query;

  const query = { status: "sent", isDeleted: false, activeStatus: 1 };
  if (tag) query.tags = tag;

  const newsletters = await Newsletter.find(query)
    .select("title excerpt coverImage sentAt tags")
    .sort({ sentAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await Newsletter.countDocuments(query);

  res.status(200).json({
    status: "success",
    data: {
      newsletters,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit),
    },
  });
});

// Get single newsletter (public)
exports.getNewsletter = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const newsletter = await Newsletter.findOne({
    _id: id,
    status: "sent",
    isDeleted: false,
    activeStatus: 1,
  }).populate("createdBy", "firstName lastName name");

  if (!newsletter) {
    return next(new AppError("Newsletter not found", 404));
  }

  res.status(200).json({
    status: "success",
    data: newsletter,
  });
});

// Admin: Create newsletter
exports.createNewsletter = catchAsync(async (req, res, next) => {
  const { title, content, excerpt, tags, scheduledAt } = req.body;

  const newsletter = await Newsletter.create({
    title,
    content,
    excerpt,
    tags: tags || [],
    scheduledAt,
    createdBy: req.user._id,
  });

  res.status(201).json({
    status: "success",
    data: newsletter,
  });
});

// Admin: Update newsletter
exports.updateNewsletter = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { title, content, excerpt, tags, scheduledAt, status } = req.body;

  const newsletter = await Newsletter.findById(id);

  if (!newsletter || newsletter.isDeleted || newsletter.activeStatus === 0) {
    return next(new AppError("Newsletter not found", 404));
  }

  if (newsletter.status === "sent") {
    return next(new AppError("Cannot update a newsletter that has already been sent", 400));
  }

  // Update fields
  if (title) newsletter.title = title;
  if (content) newsletter.content = content;
  if (excerpt) newsletter.excerpt = excerpt;
  if (tags) newsletter.tags = tags;
  if (scheduledAt) newsletter.scheduledAt = scheduledAt;
  if (status) newsletter.status = status;

  await newsletter.save();

  res.status(200).json({
    status: "success",
    data: newsletter,
  });
});

// Admin: Get all newsletters (including drafts)
exports.getAllNewsletters = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, status } = req.query;

  const query = { isDeleted: false, activeStatus: 1 };
  if (status) query.status = status;

  const newsletters = await Newsletter.find(query)
    .populate("createdBy", "firstName lastName name")
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await Newsletter.countDocuments(query);

  res.status(200).json({
    status: "success",
    data: {
      newsletters,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit),
    },
  });
});

// Admin: Delete newsletter
exports.deleteNewsletter = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const newsletter = await Newsletter.findById(id);

  if (!newsletter || newsletter.isDeleted || newsletter.activeStatus === 0) {
    return next(new AppError("Newsletter not found", 404));
  }

  newsletter.isDeleted = true;
  newsletter.activeStatus = 0;
  await newsletter.save();

  res.status(200).json({
    status: "success",
    message: "Newsletter deleted successfully",
  });
});

// Admin: Get subscription statistics
exports.getSubscriptionStats = catchAsync(async (req, res, next) => {
  const totalSubscriptions = await NewsletterSubscription.countDocuments({ isActive: true });
  const totalUnsubscribed = await NewsletterSubscription.countDocuments({ isActive: false });
  const recentSubscriptions = await NewsletterSubscription.countDocuments({
    isActive: true,
    subscribedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
  });

  res.status(200).json({
    status: "success",
    data: {
      totalSubscriptions,
      totalUnsubscribed,
      recentSubscriptions,
    },
  });
});

// Admin: Get all subscriptions
exports.getSubscriptions = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, isActive } = req.query;

  const query = {};
  if (isActive !== undefined) query.isActive = isActive === "true";

  const subscriptions = await NewsletterSubscription.find(query)
    .sort({ subscribedAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await NewsletterSubscription.countDocuments(query);

  res.status(200).json({
    status: "success",
    data: {
      subscriptions,
      total,
      page: parseInt(page),
      totalPages: Math.ceil(total / limit),
    },
  });
});
