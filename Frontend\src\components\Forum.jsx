import React, { useState, useEffect } from 'react';
import { 
  <PERSON>aComments, 
  FaPlus, 
  FaThumbsUp, 
  FaThumbsDown, 
  FaReply, 
  FaEye,
  FaPin,
  FaLock,
  FaFilter
} from 'react-icons/fa';
import './Forum.css';

const Forum = ({ courseId, userRole, userId }) => {
  const [topics, setTopics] = useState([]);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filter, setFilter] = useState('all');
  const [newTopic, setNewTopic] = useState({
    title: '',
    description: '',
    category: 'general',
    tags: []
  });
  const [newPost, setNewPost] = useState('');

  useEffect(() => {
    fetchTopics();
  }, [courseId, filter]);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch(
        `/api/forum/course/${courseId}?category=${filter !== 'all' ? filter : ''}`,
        {
          headers: {
            'x-auth-token': token,
          },
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        setTopics(data.data.topics);
      }
    } catch (error) {
      console.error('Error fetching forum topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const createTopic = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/forum/course/${courseId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token,
        },
        body: JSON.stringify(newTopic),
      });

      if (response.ok) {
        setShowCreateForm(false);
        setNewTopic({ title: '', description: '', category: 'general', tags: [] });
        fetchTopics();
      }
    } catch (error) {
      console.error('Error creating topic:', error);
    }
  };

  const addPost = async (topicId) => {
    if (!newPost.trim()) return;

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/forum/topic/${topicId}/posts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token,
        },
        body: JSON.stringify({ content: newPost }),
      });

      if (response.ok) {
        setNewPost('');
        // Refresh the selected topic
        fetchTopicDetails(topicId);
      }
    } catch (error) {
      console.error('Error adding post:', error);
    }
  };

  const fetchTopicDetails = async (topicId) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/forum/topic/${topicId}`, {
        headers: {
          'x-auth-token': token,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedTopic(data.data);
      }
    } catch (error) {
      console.error('Error fetching topic details:', error);
    }
  };

  const voteOnPost = async (topicId, postId, voteType) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/forum/topic/${topicId}/posts/${postId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token,
        },
        body: JSON.stringify({ voteType }),
      });

      if (response.ok) {
        fetchTopicDetails(topicId);
      }
    } catch (error) {
      console.error('Error voting on post:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'qa': return '❓';
      case 'discussion': return '💬';
      case 'announcement': return '📢';
      default: return '💭';
    }
  };

  if (loading) {
    return <div className="forum-loading">Loading forum...</div>;
  }

  return (
    <div className="forum-container">
      {!selectedTopic ? (
        // Topics List View
        <div className="forum-topics">
          <div className="forum-header">
            <div className="forum-title">
              <FaComments className="forum-icon" />
              <h2>Course Forum</h2>
            </div>
            <div className="forum-controls">
              <select 
                value={filter} 
                onChange={(e) => setFilter(e.target.value)}
                className="category-filter"
              >
                <option value="all">All Categories</option>
                <option value="general">General</option>
                <option value="qa">Q&A</option>
                <option value="discussion">Discussion</option>
                <option value="announcement">Announcements</option>
              </select>
              <button 
                onClick={() => setShowCreateForm(true)}
                className="create-topic-btn"
              >
                <FaPlus /> New Topic
              </button>
            </div>
          </div>

          {showCreateForm && (
            <div className="create-topic-form">
              <form onSubmit={createTopic}>
                <input
                  type="text"
                  placeholder="Topic title"
                  value={newTopic.title}
                  onChange={(e) => setNewTopic(prev => ({ ...prev, title: e.target.value }))}
                  required
                />
                <textarea
                  placeholder="Topic description"
                  value={newTopic.description}
                  onChange={(e) => setNewTopic(prev => ({ ...prev, description: e.target.value }))}
                  rows="3"
                />
                <select
                  value={newTopic.category}
                  onChange={(e) => setNewTopic(prev => ({ ...prev, category: e.target.value }))}
                >
                  <option value="general">General</option>
                  <option value="qa">Q&A</option>
                  <option value="discussion">Discussion</option>
                  {userRole === 'admin' && <option value="announcement">Announcement</option>}
                </select>
                <div className="form-actions">
                  <button type="button" onClick={() => setShowCreateForm(false)}>Cancel</button>
                  <button type="submit">Create Topic</button>
                </div>
              </form>
            </div>
          )}

          <div className="topics-list">
            {topics.length === 0 ? (
              <div className="no-topics">
                <FaComments size={48} />
                <p>No topics yet. Be the first to start a discussion!</p>
              </div>
            ) : (
              topics.map(topic => (
                <div 
                  key={topic._id} 
                  className={`topic-card ${topic.isPinned ? 'pinned' : ''}`}
                  onClick={() => fetchTopicDetails(topic._id)}
                >
                  <div className="topic-header">
                    <div className="topic-meta">
                      <span className="category-badge">
                        {getCategoryIcon(topic.category)} {topic.category}
                      </span>
                      {topic.isPinned && <FaPin className="pin-icon" />}
                      {topic.isLocked && <FaLock className="lock-icon" />}
                    </div>
                    <div className="topic-stats">
                      <span><FaEye /> {topic.viewCount}</span>
                      <span><FaComments /> {topic.posts?.length || 0}</span>
                    </div>
                  </div>
                  <h3 className="topic-title">{topic.title}</h3>
                  <p className="topic-description">{topic.description}</p>
                  <div className="topic-footer">
                    <div className="topic-author">
                      By {topic.creator?.firstName} {topic.creator?.lastName} • {formatDate(topic.createdAt)}
                    </div>
                    {topic.tags && topic.tags.length > 0 && (
                      <div className="topic-tags">
                        {topic.tags.map((tag, index) => (
                          <span key={index} className="tag">{tag}</span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      ) : (
        // Topic Detail View
        <div className="topic-detail">
          <div className="topic-detail-header">
            <button 
              onClick={() => setSelectedTopic(null)}
              className="back-btn"
            >
              ← Back to Topics
            </button>
            <div className="topic-info">
              <div className="topic-meta">
                <span className="category-badge">
                  {getCategoryIcon(selectedTopic.category)} {selectedTopic.category}
                </span>
                {selectedTopic.isPinned && <FaPin className="pin-icon" />}
                {selectedTopic.isLocked && <FaLock className="lock-icon" />}
              </div>
              <h1>{selectedTopic.title}</h1>
              <p>{selectedTopic.description}</p>
              <div className="topic-author">
                By {selectedTopic.creator?.firstName} {selectedTopic.creator?.lastName} • {formatDate(selectedTopic.createdAt)}
              </div>
            </div>
          </div>

          <div className="posts-section">
            {selectedTopic.posts?.map(post => (
              <div key={post._id} className="post-card">
                <div className="post-header">
                  <div className="post-author">
                    <strong>{post.user?.firstName} {post.user?.lastName}</strong>
                    <span className="post-date">{formatDate(post.createdAt)}</span>
                  </div>
                  <div className="post-votes">
                    <button 
                      onClick={() => voteOnPost(selectedTopic._id, post._id, 'upvote')}
                      className="vote-btn upvote"
                    >
                      <FaThumbsUp /> {post.upvotes?.length || 0}
                    </button>
                    <button 
                      onClick={() => voteOnPost(selectedTopic._id, post._id, 'downvote')}
                      className="vote-btn downvote"
                    >
                      <FaThumbsDown /> {post.downvotes?.length || 0}
                    </button>
                  </div>
                </div>
                <div className="post-content">
                  {post.content}
                </div>
              </div>
            ))}

            {!selectedTopic.isLocked && (
              <div className="add-post-form">
                <textarea
                  value={newPost}
                  onChange={(e) => setNewPost(e.target.value)}
                  placeholder="Add your response..."
                  rows="4"
                />
                <button 
                  onClick={() => addPost(selectedTopic._id)}
                  disabled={!newPost.trim()}
                  className="post-btn"
                >
                  <FaReply /> Post Reply
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Forum;
